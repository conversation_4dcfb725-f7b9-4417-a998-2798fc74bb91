import { keybinding<PERSON>abelBackground, keybindingLabelBorder, keybindingLabelBottomBorder, keybindingLabelForeground, asCssVariable, widgetShadow, buttonForeground, buttonSeparator, buttonBackground, buttonHoverBackground, buttonSecondaryForeground, buttonSecondaryBackground, buttonSecondaryHoverBackground, buttonBorder, progressBarBackground, inputActiveOptionBorder, inputActiveOptionForeground, inputActiveOptionBackground, editorWidgetBackground, editorWidgetForeground, contrastBorder, checkboxBorder, checkboxBackground, checkboxForeground, problemsErrorIconForeground, problemsWarningIconForeground, problemsInfoIconForeground, inputBackground, inputForeground, inputBorder, textLinkForeground, inputValidationInfoBorder, inputValidationInfoBackground, inputValidationInfoForeground, inputValidationWarningBorder, inputValidationWarningBackground, inputValidationWarningForeground, inputValidationErrorBorder, inputValidationErrorBackground, inputValidationErrorForeground, listFilterWidgetBackground, listFilterWidgetNoMatchesOutline, listFilterWidgetOutline, listFilterWidgetShadow, badgeBackground, badgeForeground, breadcrumbsBackground, breadcrumbsForeground, breadcrumbsFocusForeground, breadcrumbsActiveSelectionForeground, activeContrastBorder, listActiveSelectionBackground, listActiveSelectionForeground, listActiveSelectionIconForeground, listDropOverBackground, listFocusAndSelectionOutline, listFocusBackground, listFocusForeground, listFocusOutline, listHoverBackground, listHoverForeground, listInactiveFocusBackground, listInactiveFocusOutline, listInactiveSelectionBackground, listInactiveSelectionForeground, listInactiveSelectionIconForeground, tableColumnsBorder, tableOddRowsBackgroundColor, treeIndentGuidesStroke, asCssVariableWithDefault, editorWidgetBorder, focusBorder, pickerGroupForeground, quickInputListFocusBackground, quickInputListFocusForeground, quickInputListFocusIconForeground, selectBackground, selectBorder, selectForeground, selectListBackground, treeInactiveIndentGuidesStroke, menuBorder, menuForeground, menuBackground, menuSelectionForeground, menuSelectionBackground, menuSelectionBorder, menuSeparatorBackground, scrollbarShadow, scrollbarSliderActiveBackground, scrollbarSliderBackground, scrollbarSliderHoverBackground, listDropBetweenBackground, radioActiveBackground, radioActiveForeground, radioInactiveBackground, radioInactiveForeground, radioInactiveBorder, radioInactiveHoverBackground, radioActiveBorder } from '../common/colorRegistry.js';
import { Color } from '../../../base/common/color.js';
function overrideStyles(override, styles) {
    const result = { ...styles };
    for (const key in override) {
        const val = override[key];
        result[key] = val !== undefined ? asCssVariable(val) : undefined;
    }
    return result;
}
export const defaultKeybindingLabelStyles = {
    keybindingLabelBackground: asCssVariable(keybindingLabelBackground),
    keybindingLabelForeground: asCssVariable(keybindingLabelForeground),
    keybindingLabelBorder: asCssVariable(keybindingLabelBorder),
    keybindingLabelBottomBorder: asCssVariable(keybindingLabelBottomBorder),
    keybindingLabelShadow: asCssVariable(widgetShadow)
};
export function getKeybindingLabelStyles(override) {
    return overrideStyles(override, defaultKeybindingLabelStyles);
}
export const defaultButtonStyles = {
    buttonForeground: asCssVariable(buttonForeground),
    buttonSeparator: asCssVariable(buttonSeparator),
    buttonBackground: asCssVariable(buttonBackground),
    buttonHoverBackground: asCssVariable(buttonHoverBackground),
    buttonSecondaryForeground: asCssVariable(buttonSecondaryForeground),
    buttonSecondaryBackground: asCssVariable(buttonSecondaryBackground),
    buttonSecondaryHoverBackground: asCssVariable(buttonSecondaryHoverBackground),
    buttonBorder: asCssVariable(buttonBorder),
};
export function getButtonStyles(override) {
    return overrideStyles(override, defaultButtonStyles);
}
export const defaultProgressBarStyles = {
    progressBarBackground: asCssVariable(progressBarBackground)
};
export function getProgressBarStyles(override) {
    return overrideStyles(override, defaultProgressBarStyles);
}
export const defaultToggleStyles = {
    inputActiveOptionBorder: asCssVariable(inputActiveOptionBorder),
    inputActiveOptionForeground: asCssVariable(inputActiveOptionForeground),
    inputActiveOptionBackground: asCssVariable(inputActiveOptionBackground)
};
export const defaultRadioStyles = {
    activeForeground: asCssVariable(radioActiveForeground),
    activeBackground: asCssVariable(radioActiveBackground),
    activeBorder: asCssVariable(radioActiveBorder),
    inactiveForeground: asCssVariable(radioInactiveForeground),
    inactiveBackground: asCssVariable(radioInactiveBackground),
    inactiveBorder: asCssVariable(radioInactiveBorder),
    inactiveHoverBackground: asCssVariable(radioInactiveHoverBackground),
};
export function getToggleStyles(override) {
    return overrideStyles(override, defaultToggleStyles);
}
export const defaultCheckboxStyles = {
    checkboxBackground: asCssVariable(checkboxBackground),
    checkboxBorder: asCssVariable(checkboxBorder),
    checkboxForeground: asCssVariable(checkboxForeground)
};
export function getCheckboxStyles(override) {
    return overrideStyles(override, defaultCheckboxStyles);
}
export const defaultDialogStyles = {
    dialogBackground: asCssVariable(editorWidgetBackground),
    dialogForeground: asCssVariable(editorWidgetForeground),
    dialogShadow: asCssVariable(widgetShadow),
    dialogBorder: asCssVariable(contrastBorder),
    errorIconForeground: asCssVariable(problemsErrorIconForeground),
    warningIconForeground: asCssVariable(problemsWarningIconForeground),
    infoIconForeground: asCssVariable(problemsInfoIconForeground),
    textLinkForeground: asCssVariable(textLinkForeground)
};
export function getDialogStyle(override) {
    return overrideStyles(override, defaultDialogStyles);
}
export const defaultInputBoxStyles = {
    inputBackground: asCssVariable(inputBackground),
    inputForeground: asCssVariable(inputForeground),
    inputBorder: asCssVariable(inputBorder),
    inputValidationInfoBorder: asCssVariable(inputValidationInfoBorder),
    inputValidationInfoBackground: asCssVariable(inputValidationInfoBackground),
    inputValidationInfoForeground: asCssVariable(inputValidationInfoForeground),
    inputValidationWarningBorder: asCssVariable(inputValidationWarningBorder),
    inputValidationWarningBackground: asCssVariable(inputValidationWarningBackground),
    inputValidationWarningForeground: asCssVariable(inputValidationWarningForeground),
    inputValidationErrorBorder: asCssVariable(inputValidationErrorBorder),
    inputValidationErrorBackground: asCssVariable(inputValidationErrorBackground),
    inputValidationErrorForeground: asCssVariable(inputValidationErrorForeground)
};
export function getInputBoxStyle(override) {
    return overrideStyles(override, defaultInputBoxStyles);
}
export const defaultFindWidgetStyles = {
    listFilterWidgetBackground: asCssVariable(listFilterWidgetBackground),
    listFilterWidgetOutline: asCssVariable(listFilterWidgetOutline),
    listFilterWidgetNoMatchesOutline: asCssVariable(listFilterWidgetNoMatchesOutline),
    listFilterWidgetShadow: asCssVariable(listFilterWidgetShadow),
    inputBoxStyles: defaultInputBoxStyles,
    toggleStyles: defaultToggleStyles
};
export const defaultCountBadgeStyles = {
    badgeBackground: asCssVariable(badgeBackground),
    badgeForeground: asCssVariable(badgeForeground),
    badgeBorder: asCssVariable(contrastBorder)
};
export function getCountBadgeStyle(override) {
    return overrideStyles(override, defaultCountBadgeStyles);
}
export const defaultBreadcrumbsWidgetStyles = {
    breadcrumbsBackground: asCssVariable(breadcrumbsBackground),
    breadcrumbsForeground: asCssVariable(breadcrumbsForeground),
    breadcrumbsHoverForeground: asCssVariable(breadcrumbsFocusForeground),
    breadcrumbsFocusForeground: asCssVariable(breadcrumbsFocusForeground),
    breadcrumbsFocusAndSelectionForeground: asCssVariable(breadcrumbsActiveSelectionForeground)
};
export function getBreadcrumbsWidgetStyles(override) {
    return overrideStyles(override, defaultBreadcrumbsWidgetStyles);
}
export const defaultListStyles = {
    listBackground: undefined,
    listInactiveFocusForeground: undefined,
    listFocusBackground: asCssVariable(listFocusBackground),
    listFocusForeground: asCssVariable(listFocusForeground),
    listFocusOutline: asCssVariable(listFocusOutline),
    listActiveSelectionBackground: asCssVariable(listActiveSelectionBackground),
    listActiveSelectionForeground: asCssVariable(listActiveSelectionForeground),
    listActiveSelectionIconForeground: asCssVariable(listActiveSelectionIconForeground),
    listFocusAndSelectionOutline: asCssVariable(listFocusAndSelectionOutline),
    listFocusAndSelectionBackground: asCssVariable(listActiveSelectionBackground),
    listFocusAndSelectionForeground: asCssVariable(listActiveSelectionForeground),
    listInactiveSelectionBackground: asCssVariable(listInactiveSelectionBackground),
    listInactiveSelectionIconForeground: asCssVariable(listInactiveSelectionIconForeground),
    listInactiveSelectionForeground: asCssVariable(listInactiveSelectionForeground),
    listInactiveFocusBackground: asCssVariable(listInactiveFocusBackground),
    listInactiveFocusOutline: asCssVariable(listInactiveFocusOutline),
    listHoverBackground: asCssVariable(listHoverBackground),
    listHoverForeground: asCssVariable(listHoverForeground),
    listDropOverBackground: asCssVariable(listDropOverBackground),
    listDropBetweenBackground: asCssVariable(listDropBetweenBackground),
    listSelectionOutline: asCssVariable(activeContrastBorder),
    listHoverOutline: asCssVariable(activeContrastBorder),
    treeIndentGuidesStroke: asCssVariable(treeIndentGuidesStroke),
    treeInactiveIndentGuidesStroke: asCssVariable(treeInactiveIndentGuidesStroke),
    treeStickyScrollBackground: undefined,
    treeStickyScrollBorder: undefined,
    treeStickyScrollShadow: asCssVariable(scrollbarShadow),
    tableColumnsBorder: asCssVariable(tableColumnsBorder),
    tableOddRowsBackgroundColor: asCssVariable(tableOddRowsBackgroundColor),
};
export function getListStyles(override) {
    return overrideStyles(override, defaultListStyles);
}
export const defaultSelectBoxStyles = {
    selectBackground: asCssVariable(selectBackground),
    selectListBackground: asCssVariable(selectListBackground),
    selectForeground: asCssVariable(selectForeground),
    decoratorRightForeground: asCssVariable(pickerGroupForeground),
    selectBorder: asCssVariable(selectBorder),
    focusBorder: asCssVariable(focusBorder),
    listFocusBackground: asCssVariable(quickInputListFocusBackground),
    listInactiveSelectionIconForeground: asCssVariable(quickInputListFocusIconForeground),
    listFocusForeground: asCssVariable(quickInputListFocusForeground),
    listFocusOutline: asCssVariableWithDefault(activeContrastBorder, Color.transparent.toString()),
    listHoverBackground: asCssVariable(listHoverBackground),
    listHoverForeground: asCssVariable(listHoverForeground),
    listHoverOutline: asCssVariable(activeContrastBorder),
    selectListBorder: asCssVariable(editorWidgetBorder),
    listBackground: undefined,
    listActiveSelectionBackground: undefined,
    listActiveSelectionForeground: undefined,
    listActiveSelectionIconForeground: undefined,
    listFocusAndSelectionBackground: undefined,
    listDropOverBackground: undefined,
    listDropBetweenBackground: undefined,
    listInactiveSelectionBackground: undefined,
    listInactiveSelectionForeground: undefined,
    listInactiveFocusBackground: undefined,
    listInactiveFocusOutline: undefined,
    listSelectionOutline: undefined,
    listFocusAndSelectionForeground: undefined,
    listFocusAndSelectionOutline: undefined,
    listInactiveFocusForeground: undefined,
    tableColumnsBorder: undefined,
    tableOddRowsBackgroundColor: undefined,
    treeIndentGuidesStroke: undefined,
    treeInactiveIndentGuidesStroke: undefined,
    treeStickyScrollBackground: undefined,
    treeStickyScrollBorder: undefined,
    treeStickyScrollShadow: undefined
};
export function getSelectBoxStyles(override) {
    return overrideStyles(override, defaultSelectBoxStyles);
}
export const defaultMenuStyles = {
    shadowColor: asCssVariable(widgetShadow),
    borderColor: asCssVariable(menuBorder),
    foregroundColor: asCssVariable(menuForeground),
    backgroundColor: asCssVariable(menuBackground),
    selectionForegroundColor: asCssVariable(menuSelectionForeground),
    selectionBackgroundColor: asCssVariable(menuSelectionBackground),
    selectionBorderColor: asCssVariable(menuSelectionBorder),
    separatorColor: asCssVariable(menuSeparatorBackground),
    scrollbarShadow: asCssVariable(scrollbarShadow),
    scrollbarSliderBackground: asCssVariable(scrollbarSliderBackground),
    scrollbarSliderHoverBackground: asCssVariable(scrollbarSliderHoverBackground),
    scrollbarSliderActiveBackground: asCssVariable(scrollbarSliderActiveBackground)
};
export function getMenuStyles(override) {
    return overrideStyles(override, defaultMenuStyles);
}
//# sourceMappingURL=data:application/json;base64,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
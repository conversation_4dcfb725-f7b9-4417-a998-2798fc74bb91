/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
export var MarshalledId;
(function (MarshalledId) {
    MarshalledId[MarshalledId["Uri"] = 1] = "Uri";
    MarshalledId[MarshalledId["Regexp"] = 2] = "Regexp";
    MarshalledId[MarshalledId["ScmResource"] = 3] = "ScmResource";
    MarshalledId[MarshalledId["ScmResourceGroup"] = 4] = "ScmResourceGroup";
    MarshalledId[MarshalledId["ScmProvider"] = 5] = "ScmProvider";
    MarshalledId[MarshalledId["CommentController"] = 6] = "CommentController";
    MarshalledId[MarshalledId["CommentThread"] = 7] = "CommentThread";
    MarshalledId[MarshalledId["CommentThreadInstance"] = 8] = "CommentThreadInstance";
    MarshalledId[MarshalledId["CommentThreadReply"] = 9] = "CommentThreadReply";
    MarshalledId[MarshalledId["CommentNode"] = 10] = "CommentNode";
    MarshalledId[MarshalledId["CommentThreadNode"] = 11] = "CommentThreadNode";
    MarshalledId[MarshalledId["TimelineActionContext"] = 12] = "TimelineActionContext";
    MarshalledId[MarshalledId["NotebookCellActionContext"] = 13] = "NotebookCellActionContext";
    MarshalledId[MarshalledId["NotebookActionContext"] = 14] = "NotebookActionContext";
    MarshalledId[MarshalledId["TerminalContext"] = 15] = "TerminalContext";
    MarshalledId[MarshalledId["TestItemContext"] = 16] = "TestItemContext";
    MarshalledId[MarshalledId["Date"] = 17] = "Date";
    MarshalledId[MarshalledId["TestMessageMenuArgs"] = 18] = "TestMessageMenuArgs";
    MarshalledId[MarshalledId["ChatViewContext"] = 19] = "ChatViewContext";
    MarshalledId[MarshalledId["LanguageModelToolResult"] = 20] = "LanguageModelToolResult";
    MarshalledId[MarshalledId["LanguageModelTextPart"] = 21] = "LanguageModelTextPart";
    MarshalledId[MarshalledId["LanguageModelPromptTsxPart"] = 22] = "LanguageModelPromptTsxPart";
    MarshalledId[MarshalledId["LanguageModelDataPart"] = 23] = "LanguageModelDataPart";
})(MarshalledId || (MarshalledId = {}));
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibWFyc2hhbGxpbmdJZHMuanMiLCJzb3VyY2VSb290IjoiZmlsZTovLy9DOi9Vc2Vycy9CaGF3ZXNoL0Rlc2t0b3AvdGVzdGluZ19wdXJwb3Nlcy90ZXN0aW5nX3B1cnBvc2VzL0Zsb3cvc3JjLyIsInNvdXJjZXMiOlsidnMvYmFzZS9jb21tb24vbWFyc2hhbGxpbmdJZHMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7OztnR0FHZ0c7QUFFaEcsTUFBTSxDQUFOLElBQWtCLFlBd0JqQjtBQXhCRCxXQUFrQixZQUFZO0lBQzdCLDZDQUFPLENBQUE7SUFDUCxtREFBTSxDQUFBO0lBQ04sNkRBQVcsQ0FBQTtJQUNYLHVFQUFnQixDQUFBO0lBQ2hCLDZEQUFXLENBQUE7SUFDWCx5RUFBaUIsQ0FBQTtJQUNqQixpRUFBYSxDQUFBO0lBQ2IsaUZBQXFCLENBQUE7SUFDckIsMkVBQWtCLENBQUE7SUFDbEIsOERBQVcsQ0FBQTtJQUNYLDBFQUFpQixDQUFBO0lBQ2pCLGtGQUFxQixDQUFBO0lBQ3JCLDBGQUF5QixDQUFBO0lBQ3pCLGtGQUFxQixDQUFBO0lBQ3JCLHNFQUFlLENBQUE7SUFDZixzRUFBZSxDQUFBO0lBQ2YsZ0RBQUksQ0FBQTtJQUNKLDhFQUFtQixDQUFBO0lBQ25CLHNFQUFlLENBQUE7SUFDZixzRkFBdUIsQ0FBQTtJQUN2QixrRkFBcUIsQ0FBQTtJQUNyQiw0RkFBMEIsQ0FBQTtJQUMxQixrRkFBcUIsQ0FBQTtBQUN0QixDQUFDLEVBeEJpQixZQUFZLEtBQVosWUFBWSxRQXdCN0IifQ==
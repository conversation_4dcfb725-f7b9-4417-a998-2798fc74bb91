/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
export var BindingId;
(function (BindingId) {
    BindingId[BindingId["GlyphInfo"] = 0] = "GlyphInfo";
    BindingId[BindingId["Cells"] = 1] = "Cells";
    BindingId[BindingId["TextureSampler"] = 2] = "TextureSampler";
    BindingId[BindingId["Texture"] = 3] = "Texture";
    BindingId[BindingId["LayoutInfoUniform"] = 4] = "LayoutInfoUniform";
    BindingId[BindingId["AtlasDimensionsUniform"] = 5] = "AtlasDimensionsUniform";
    BindingId[BindingId["ScrollOffset"] = 6] = "ScrollOffset";
})(BindingId || (BindingId = {}));
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZ3B1LmpzIiwic291cmNlUm9vdCI6ImZpbGU6Ly8vQzovVXNlcnMvQmhhd2VzaC9EZXNrdG9wL3Rlc3RpbmdfcHVycG9zZXMvdGVzdGluZ19wdXJwb3Nlcy9GbG93L3NyYy8iLCJzb3VyY2VzIjpbInZzL2VkaXRvci9icm93c2VyL2dwdS9ncHUudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7OztnR0FHZ0c7QUFRaEcsTUFBTSxDQUFOLElBQWtCLFNBUWpCO0FBUkQsV0FBa0IsU0FBUztJQUMxQixtREFBUyxDQUFBO0lBQ1QsMkNBQUssQ0FBQTtJQUNMLDZEQUFjLENBQUE7SUFDZCwrQ0FBTyxDQUFBO0lBQ1AsbUVBQWlCLENBQUE7SUFDakIsNkVBQXNCLENBQUE7SUFDdEIseURBQVksQ0FBQTtBQUNiLENBQUMsRUFSaUIsU0FBUyxLQUFULFNBQVMsUUFRMUIifQ==
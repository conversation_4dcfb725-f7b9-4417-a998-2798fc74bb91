/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import assert from 'assert';
import * as objects from '../../common/objects.js';
import { ensureNoDisposablesAreLeakedInTestSuite } from './utils.js';
const check = (one, other, msg) => {
    assert(objects.equals(one, other), msg);
    assert(objects.equals(other, one), '[reverse] ' + msg);
};
const checkNot = (one, other, msg) => {
    assert(!objects.equals(one, other), msg);
    assert(!objects.equals(other, one), '[reverse] ' + msg);
};
suite('Objects', () => {
    ensureNoDisposablesAreLeakedInTestSuite();
    test('equals', () => {
        check(null, null, 'null');
        check(undefined, undefined, 'undefined');
        check(1234, 1234, 'numbers');
        check('', '', 'empty strings');
        check('1234', '1234', 'strings');
        check([], [], 'empty arrays');
        // check(['', 123], ['', 123], 'arrays');
        check([[1, 2, 3], [4, 5, 6]], [[1, 2, 3], [4, 5, 6]], 'nested arrays');
        check({}, {}, 'empty objects');
        check({ a: 1, b: '123' }, { a: 1, b: '123' }, 'objects');
        check({ a: 1, b: '123' }, { b: '123', a: 1 }, 'objects (key order)');
        check({ a: { b: 1, c: 2 }, b: 3 }, { a: { b: 1, c: 2 }, b: 3 }, 'nested objects');
        checkNot(null, undefined, 'null != undefined');
        checkNot(null, '', 'null != empty string');
        checkNot(null, [], 'null != empty array');
        checkNot(null, {}, 'null != empty object');
        checkNot(null, 0, 'null != zero');
        checkNot(undefined, '', 'undefined != empty string');
        checkNot(undefined, [], 'undefined != empty array');
        checkNot(undefined, {}, 'undefined != empty object');
        checkNot(undefined, 0, 'undefined != zero');
        checkNot('', [], 'empty string != empty array');
        checkNot('', {}, 'empty string != empty object');
        checkNot('', 0, 'empty string != zero');
        checkNot([], {}, 'empty array != empty object');
        checkNot([], 0, 'empty array != zero');
        checkNot(0, [], 'zero != empty array');
        checkNot('1234', 1234, 'string !== number');
        checkNot([[1, 2, 3], [4, 5, 6]], [[1, 2, 3], [4, 5, 6000]], 'arrays');
        checkNot({ a: { b: 1, c: 2 }, b: 3 }, { b: 3, a: { b: 9, c: 2 } }, 'objects');
    });
    test('mixin - array', function () {
        const foo = {};
        objects.mixin(foo, { bar: [1, 2, 3] });
        assert(foo.bar);
        assert(Array.isArray(foo.bar));
        assert.strictEqual(foo.bar.length, 3);
        assert.strictEqual(foo.bar[0], 1);
        assert.strictEqual(foo.bar[1], 2);
        assert.strictEqual(foo.bar[2], 3);
    });
    test('mixin - no overwrite', function () {
        const foo = {
            bar: '123'
        };
        const bar = {
            bar: '456'
        };
        objects.mixin(foo, bar, false);
        assert.strictEqual(foo.bar, '123');
    });
    test('cloneAndChange', () => {
        const o1 = { something: 'hello' };
        const o = {
            o1: o1,
            o2: o1
        };
        assert.deepStrictEqual(objects.cloneAndChange(o, () => { }), o);
    });
    test('safeStringify', () => {
        const obj1 = {
            friend: null
        };
        const obj2 = {
            friend: null
        };
        obj1.friend = obj2;
        obj2.friend = obj1;
        const arr = [1];
        arr.push(arr);
        const circular = {
            a: 42,
            b: null,
            c: [
                obj1, obj2
            ],
            d: null,
            e: BigInt(42)
        };
        arr.push(circular);
        circular.b = circular;
        circular.d = arr;
        const result = objects.safeStringify(circular);
        assert.deepStrictEqual(JSON.parse(result), {
            a: 42,
            b: '[Circular]',
            c: [
                {
                    friend: {
                        friend: '[Circular]'
                    }
                },
                '[Circular]'
            ],
            d: [1, '[Circular]', '[Circular]'],
            e: '[BigInt 42]'
        });
    });
    test('distinct', () => {
        const base = {
            one: 'one',
            two: 2,
            three: {
                3: true
            },
            four: false
        };
        let diff = objects.distinct(base, base);
        assert.strictEqual(Object.keys(diff).length, 0);
        let obj = {};
        diff = objects.distinct(base, obj);
        assert.strictEqual(Object.keys(diff).length, 0);
        obj = {
            one: 'one',
            two: 2
        };
        diff = objects.distinct(base, obj);
        assert.strictEqual(Object.keys(diff).length, 0);
        obj = {
            three: {
                3: true
            },
            four: false
        };
        diff = objects.distinct(base, obj);
        assert.strictEqual(Object.keys(diff).length, 0);
        obj = {
            one: 'two',
            two: 2,
            three: {
                3: true
            },
            four: true
        };
        diff = objects.distinct(base, obj);
        assert.strictEqual(Object.keys(diff).length, 2);
        assert.strictEqual(diff.one, 'two');
        assert.strictEqual(diff.four, true);
        obj = {
            one: null,
            two: 2,
            three: {
                3: true
            },
            four: undefined
        };
        diff = objects.distinct(base, obj);
        assert.strictEqual(Object.keys(diff).length, 2);
        assert.strictEqual(diff.one, null);
        assert.strictEqual(diff.four, undefined);
        obj = {
            one: 'two',
            two: 3,
            three: { 3: false },
            four: true
        };
        diff = objects.distinct(base, obj);
        assert.strictEqual(Object.keys(diff).length, 4);
        assert.strictEqual(diff.one, 'two');
        assert.strictEqual(diff.two, 3);
        assert.strictEqual(diff.three?.['3'], false);
        assert.strictEqual(diff.four, true);
    });
    test('getCaseInsensitive', () => {
        const obj1 = {
            lowercase: 123,
            mIxEdCaSe: 456
        };
        assert.strictEqual(obj1.lowercase, objects.getCaseInsensitive(obj1, 'lowercase'));
        assert.strictEqual(obj1.lowercase, objects.getCaseInsensitive(obj1, 'lOwErCaSe'));
        assert.strictEqual(obj1.mIxEdCaSe, objects.getCaseInsensitive(obj1, 'MIXEDCASE'));
        assert.strictEqual(obj1.mIxEdCaSe, objects.getCaseInsensitive(obj1, 'mixedcase'));
    });
});
test('mapValues', () => {
    const obj = {
        a: 1,
        b: 2,
        c: 3
    };
    const result = objects.mapValues(obj, (value, key) => `${key}: ${value * 2}`);
    assert.deepStrictEqual(result, {
        a: 'a: 2',
        b: 'b: 4',
        c: 'c: 6',
    });
});
//# sourceMappingURL=data:application/json;base64,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
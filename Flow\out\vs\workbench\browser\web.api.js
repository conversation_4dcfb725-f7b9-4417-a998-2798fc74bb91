/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
export var Menu;
(function (Menu) {
    Menu[Menu["CommandPalette"] = 0] = "CommandPalette";
    Menu[Menu["StatusBarWindowIndicatorMenu"] = 1] = "StatusBarWindowIndicatorMenu";
})(Menu || (Menu = {}));
export var ColorScheme;
(function (ColorScheme) {
    ColorScheme["DARK"] = "dark";
    ColorScheme["LIGHT"] = "light";
    ColorScheme["HIGH_CONTRAST_LIGHT"] = "hcLight";
    ColorScheme["HIGH_CONTRAST_DARK"] = "hcDark";
})(ColorScheme || (ColorScheme = {}));
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoid2ViLmFwaS5qcyIsInNvdXJjZVJvb3QiOiJmaWxlOi8vL0M6L1VzZXJzL0JoYXdlc2gvRGVza3RvcC90ZXN0aW5nX3B1cnBvc2VzL3Rlc3RpbmdfcHVycG9zZXMvRmxvdy9zcmMvIiwic291cmNlcyI6WyJ2cy93b3JrYmVuY2gvYnJvd3Nlci93ZWIuYXBpLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Z0dBR2dHO0FBc2hCaEcsTUFBTSxDQUFOLElBQVksSUFHWDtBQUhELFdBQVksSUFBSTtJQUNmLG1EQUFjLENBQUE7SUFDZCwrRUFBNEIsQ0FBQTtBQUM3QixDQUFDLEVBSFcsSUFBSSxLQUFKLElBQUksUUFHZjtBQW1HRCxNQUFNLENBQU4sSUFBWSxXQUtYO0FBTEQsV0FBWSxXQUFXO0lBQ3RCLDRCQUFhLENBQUE7SUFDYiw4QkFBZSxDQUFBO0lBQ2YsOENBQStCLENBQUE7SUFDL0IsNENBQTZCLENBQUE7QUFDOUIsQ0FBQyxFQUxXLFdBQVcsS0FBWCxXQUFXLFFBS3RCIn0=
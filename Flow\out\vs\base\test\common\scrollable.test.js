/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import assert from 'assert';
import { SmoothScrollingOperation } from '../../common/scrollable.js';
import { ensureNoDisposablesAreLeakedInTestSuite } from './utils.js';
class TestSmoothScrollingOperation extends SmoothScrollingOperation {
    constructor(from, to, viewportSize, startTime, duration) {
        duration = duration + 10;
        startTime = startTime - 10;
        super({ scrollLeft: 0, scrollTop: from, width: 0, height: viewportSize }, { scrollLeft: 0, scrollTop: to, width: 0, height: viewportSize }, startTime, duration);
    }
    testTick(now) {
        return this._tick(now);
    }
}
suite('SmoothScrollingOperation', () => {
    const VIEWPORT_HEIGHT = 800;
    const ANIMATION_DURATION = 125;
    const LINE_HEIGHT = 20;
    ensureNoDisposablesAreLeakedInTestSuite();
    function extractLines(scrollable, now) {
        const scrollTop = scrollable.testTick(now).scrollTop;
        const scrollBottom = scrollTop + VIEWPORT_HEIGHT;
        const startLineNumber = Math.floor(scrollTop / LINE_HEIGHT);
        const endLineNumber = Math.ceil(scrollBottom / LINE_HEIGHT);
        return [startLineNumber, endLineNumber];
    }
    function simulateSmoothScroll(from, to) {
        const scrollable = new TestSmoothScrollingOperation(from, to, VIEWPORT_HEIGHT, 0, ANIMATION_DURATION);
        const result = [];
        let resultLen = 0;
        result[resultLen++] = extractLines(scrollable, 0);
        result[resultLen++] = extractLines(scrollable, 25);
        result[resultLen++] = extractLines(scrollable, 50);
        result[resultLen++] = extractLines(scrollable, 75);
        result[resultLen++] = extractLines(scrollable, 100);
        result[resultLen++] = extractLines(scrollable, 125);
        return result;
    }
    function assertSmoothScroll(from, to, expected) {
        const actual = simulateSmoothScroll(from, to);
        assert.deepStrictEqual(actual, expected);
    }
    test('scroll 25 lines (40 fit)', () => {
        assertSmoothScroll(0, 500, [
            [5, 46],
            [14, 55],
            [20, 61],
            [23, 64],
            [24, 65],
            [25, 65],
        ]);
    });
    test('scroll 75 lines (40 fit)', () => {
        assertSmoothScroll(0, 1500, [
            [15, 56],
            [44, 85],
            [62, 103],
            [71, 112],
            [74, 115],
            [75, 115],
        ]);
    });
    test('scroll 100 lines (40 fit)', () => {
        assertSmoothScroll(0, 2000, [
            [20, 61],
            [59, 100],
            [82, 123],
            [94, 135],
            [99, 140],
            [100, 140],
        ]);
    });
    test('scroll 125 lines (40 fit)', () => {
        assertSmoothScroll(0, 2500, [
            [16, 57],
            [29, 70],
            [107, 148],
            [119, 160],
            [124, 165],
            [125, 165],
        ]);
    });
    test('scroll 500 lines (40 fit)', () => {
        assertSmoothScroll(0, 10000, [
            [16, 57],
            [29, 70],
            [482, 523],
            [494, 535],
            [499, 540],
            [500, 540],
        ]);
    });
});
//# sourceMappingURL=data:application/json;base64,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
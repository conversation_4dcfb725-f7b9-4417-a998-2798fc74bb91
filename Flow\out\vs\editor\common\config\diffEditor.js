/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
export const diffEditorDefaultOptions = {
    enableSplitViewResizing: true,
    splitViewDefaultRatio: 0.5,
    renderSideBySide: true,
    renderMarginRevertIcon: true,
    renderGutterMenu: true,
    maxComputationTime: 5000,
    maxFileSize: 50,
    ignoreTrimWhitespace: true,
    renderIndicators: true,
    originalEditable: false,
    diffCodeLens: false,
    renderOverviewRuler: true,
    diffWordWrap: 'inherit',
    diffAlgorithm: 'advanced',
    accessibilityVerbose: false,
    experimental: {
        showMoves: false,
        showEmptyDecorations: true,
        useTrueInlineView: false,
    },
    hideUnchangedRegions: {
        enabled: false,
        contextLineCount: 3,
        minimumLineCount: 3,
        revealLineCount: 20,
    },
    isInEmbeddedEditor: false,
    onlyShowAccessibleDiffViewer: false,
    renderSideBySideInlineBreakpoint: 900,
    useInlineViewWhenSpaceIsLimited: true,
    compactMode: false,
};
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZGlmZkVkaXRvci5qcyIsInNvdXJjZVJvb3QiOiJmaWxlOi8vL0M6L1VzZXJzL0JoYXdlc2gvRGVza3RvcC90ZXN0aW5nX3B1cnBvc2VzL3Rlc3RpbmdfcHVycG9zZXMvRmxvdy9zcmMvIiwic291cmNlcyI6WyJ2cy9lZGl0b3IvY29tbW9uL2NvbmZpZy9kaWZmRWRpdG9yLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Z0dBR2dHO0FBSWhHLE1BQU0sQ0FBQyxNQUFNLHdCQUF3QixHQUFHO0lBQ3ZDLHVCQUF1QixFQUFFLElBQUk7SUFDN0IscUJBQXFCLEVBQUUsR0FBRztJQUMxQixnQkFBZ0IsRUFBRSxJQUFJO0lBQ3RCLHNCQUFzQixFQUFFLElBQUk7SUFDNUIsZ0JBQWdCLEVBQUUsSUFBSTtJQUN0QixrQkFBa0IsRUFBRSxJQUFJO0lBQ3hCLFdBQVcsRUFBRSxFQUFFO0lBQ2Ysb0JBQW9CLEVBQUUsSUFBSTtJQUMxQixnQkFBZ0IsRUFBRSxJQUFJO0lBQ3RCLGdCQUFnQixFQUFFLEtBQUs7SUFDdkIsWUFBWSxFQUFFLEtBQUs7SUFDbkIsbUJBQW1CLEVBQUUsSUFBSTtJQUN6QixZQUFZLEVBQUUsU0FBUztJQUN2QixhQUFhLEVBQUUsVUFBVTtJQUN6QixvQkFBb0IsRUFBRSxLQUFLO0lBQzNCLFlBQVksRUFBRTtRQUNiLFNBQVMsRUFBRSxLQUFLO1FBQ2hCLG9CQUFvQixFQUFFLElBQUk7UUFDMUIsaUJBQWlCLEVBQUUsS0FBSztLQUN4QjtJQUNELG9CQUFvQixFQUFFO1FBQ3JCLE9BQU8sRUFBRSxLQUFLO1FBQ2QsZ0JBQWdCLEVBQUUsQ0FBQztRQUNuQixnQkFBZ0IsRUFBRSxDQUFDO1FBQ25CLGVBQWUsRUFBRSxFQUFFO0tBQ25CO0lBQ0Qsa0JBQWtCLEVBQUUsS0FBSztJQUN6Qiw0QkFBNEIsRUFBRSxLQUFLO0lBQ25DLGdDQUFnQyxFQUFFLEdBQUc7SUFDckMsK0JBQStCLEVBQUUsSUFBSTtJQUNyQyxXQUFXLEVBQUUsS0FBSztDQUNtQixDQUFDIn0=
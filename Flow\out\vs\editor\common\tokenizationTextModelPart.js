/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
export var BackgroundTokenizationState;
(function (BackgroundTokenizationState) {
    BackgroundTokenizationState[BackgroundTokenizationState["InProgress"] = 1] = "InProgress";
    BackgroundTokenizationState[BackgroundTokenizationState["Completed"] = 2] = "Completed";
})(BackgroundTokenizationState || (BackgroundTokenizationState = {}));
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidG9rZW5pemF0aW9uVGV4dE1vZGVsUGFydC5qcyIsInNvdXJjZVJvb3QiOiJmaWxlOi8vL0M6L1VzZXJzL0JoYXdlc2gvRGVza3RvcC90ZXN0aW5nX3B1cnBvc2VzL3Rlc3RpbmdfcHVycG9zZXMvRmxvdy9zcmMvIiwic291cmNlcyI6WyJ2cy9lZGl0b3IvY29tbW9uL3Rva2VuaXphdGlvblRleHRNb2RlbFBhcnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7OztnR0FHZ0c7QUErRmhHLE1BQU0sQ0FBTixJQUFrQiwyQkFHakI7QUFIRCxXQUFrQiwyQkFBMkI7SUFDNUMseUZBQWMsQ0FBQTtJQUNkLHVGQUFhLENBQUE7QUFDZCxDQUFDLEVBSGlCLDJCQUEyQixLQUEzQiwyQkFBMkIsUUFHNUMifQ==